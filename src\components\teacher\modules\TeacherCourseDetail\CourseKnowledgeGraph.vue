<template>
  <div class="course-knowledge-graph">
    <!-- 页面标题 -->
    <div class="section-header">
      <h2 class="section-title">知识图谱</h2>
      <div class="section-actions">
        <button class="btn btn-primary" @click="generateGraph">
          <i class="btn-icon generate-icon"></i>
          生成图谱
        </button>
        <button class="btn btn-secondary" @click="editGraph">
          <i class="btn-icon edit-icon"></i>
          编辑图谱
        </button>
        <button class="btn btn-outline" @click="exportGraph">
          <i class="btn-icon export-icon"></i>
          导出
        </button>
      </div>
    </div>

    <!-- 图谱控制面板 -->
    <div class="graph-controls">
      <div class="control-group">
        <label class="control-label">显示模式：</label>
        <select v-model="displayMode" class="control-select" @change="updateGraphDisplay">
          <option value="hierarchy">层次结构</option>
          <option value="network">网络图</option>
          <option value="tree">树状图</option>
        </select>
      </div>
      <div class="control-group">
        <label class="control-label">节点类型：</label>
        <div class="checkbox-group">
          <label class="checkbox-item">
            <input type="checkbox" v-model="showConcepts" @change="filterNodes">
            <span>概念</span>
          </label>
          <label class="checkbox-item">
            <input type="checkbox" v-model="showSkills" @change="filterNodes">
            <span>技能</span>
          </label>
          <label class="checkbox-item">
            <input type="checkbox" v-model="showTopics" @change="filterNodes">
            <span>主题</span>
          </label>
        </div>
      </div>
      <div class="control-group">
        <button class="btn btn-small btn-outline" @click="resetView">
          <i class="reset-icon"></i>
          重置视图
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p class="loading-text">正在生成知识图谱...</p>
    </div>

    <!-- 知识图谱容器 -->
    <div v-else-if="graphData" class="graph-container">
      <div ref="graphCanvas" class="graph-canvas" id="knowledge-graph-canvas">
        <!-- 这里将渲染知识图谱 -->
        <div class="graph-placeholder">
          <div class="placeholder-content">
            <div class="graph-icon">🕸️</div>
            <h3>知识图谱可视化区域</h3>
            <p>图谱将在此处显示</p>
            <div class="graph-stats">
              <div class="stat-item">
                <span class="stat-number">{{ graphData.nodes.length }}</span>
                <span class="stat-label">知识点</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ graphData.edges.length }}</span>
                <span class="stat-label">关联</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ graphData.clusters.length }}</span>
                <span class="stat-label">集群</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 图谱信息面板 -->
      <div class="graph-info-panel">
        <div class="panel-section">
          <h4 class="panel-title">图谱统计</h4>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-value">{{ graphData.nodes.length }}</div>
              <div class="stat-name">知识节点</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ graphData.edges.length }}</div>
              <div class="stat-name">关联边</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ graphData.clusters.length }}</div>
              <div class="stat-name">知识集群</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ calculateComplexity() }}</div>
              <div class="stat-name">复杂度</div>
            </div>
          </div>
        </div>

        <div class="panel-section">
          <h4 class="panel-title">核心概念</h4>
          <div class="concepts-list">
            <div
              v-for="concept in coreConceptsList"
              :key="concept.id"
              class="concept-item"
              :class="concept.importance"
            >
              <span class="concept-name">{{ concept.name }}</span>
              <span class="concept-weight">{{ concept.weight }}</span>
            </div>
          </div>
        </div>

        <div class="panel-section">
          <h4 class="panel-title">学习路径</h4>
          <div class="learning-paths">
            <div
              v-for="path in learningPaths"
              :key="path.id"
              class="path-item"
            >
              <div class="path-name">{{ path.name }}</div>
              <div class="path-steps">{{ path.steps.length }} 个步骤</div>
              <button class="btn btn-small btn-outline" @click="viewPath(path)">
                查看路径
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">🕸️</div>
      <h3 class="empty-title">暂无知识图谱</h3>
      <p class="empty-description">基于课程内容生成知识图谱，帮助学生理解知识结构</p>
      <button class="btn btn-primary" @click="generateGraph">
        <i class="generate-icon"></i>
        生成知识图谱
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import {
  ensureKnowledgeGraph,
  getGraphList,
  createGraph,
  updateGraph,
  getNodeList
} from '@/api/graph';

// 定义props
const props = defineProps({
  courseId: {
    type: [String, Number],
    required: true
  }
});

// 定义emits
const emit = defineEmits(['refresh']);

// 响应式数据
const loading = ref(false);
const graphData = ref(null);
const graphCanvas = ref(null);

// 控制选项
const displayMode = ref('hierarchy');
const showConcepts = ref(true);
const showSkills = ref(true);
const showTopics = ref(true);

// 核心概念列表
const coreConceptsList = ref([]);

// 学习路径
const learningPaths = ref([]);

// 加载知识图谱数据
const loadGraphData = async () => {
  loading.value = true;
  try {
    console.log('开始加载知识图谱数据，课程ID:', props.courseId);

    // 第一步：确保课程有对应的知识图谱数据记录
    console.log('正在确保课程知识图谱数据存在...');
    const graphDataResult = await ensureKnowledgeGraph(
      props.courseId,
      '课程知识图谱',
      '课程知识图谱结构数据'
    );
    console.log('知识图谱数据确保结果:', graphDataResult);

    // 第二步：如果有实际的图谱数据，加载节点信息
    if (graphDataResult.data && graphDataResult.data.id) {
      try {
        const nodeList = await getNodeList(graphDataResult.data.id);
        console.log('获取到的节点数据:', nodeList);

        // 如果有节点数据，使用实际数据
        if (nodeList && nodeList.length > 0) {
          graphData.value = {
            nodes: nodeList,
            edges: [], // 可以进一步获取连线数据
            clusters: []
          };
          console.log('使用实际的知识图谱数据');
        } else {
          // 如果没有节点数据，使用模拟数据
          loadMockGraphData();
        }
      } catch (nodeError) {
        console.warn('获取节点数据失败，使用模拟数据:', nodeError);
        loadMockGraphData();
      }
    } else {
      // 如果没有图谱数据，使用模拟数据
      loadMockGraphData();
    }

  } catch (error) {
    console.error('加载知识图谱数据失败:', error);
    // 加载失败时使用模拟数据
    loadMockGraphData();
  } finally {
    loading.value = false;
  }
};

// 加载模拟知识图谱数据
const loadMockGraphData = () => {
  console.log('使用模拟的知识图谱数据');
  // 模拟土木工程结构力学知识图谱数据
  graphData.value = {
    nodes: [
      { id: 1, name: '力学基础', type: 'concept', importance: 'high' },
      { id: 2, name: '静力平衡', type: 'concept', importance: 'high' },
      { id: 3, name: '几何组成', type: 'concept', importance: 'high' },
      { id: 4, name: '内力分析', type: 'skill', importance: 'high' },
      { id: 5, name: '位移计算', type: 'skill', importance: 'medium' },
      { id: 6, name: '虚功原理', type: 'concept', importance: 'medium' },
      { id: 7, name: '单位荷载法', type: 'skill', importance: 'medium' },
      { id: 8, name: '静定梁', type: 'topic', importance: 'high' },
      { id: 9, name: '静定刚架', type: 'topic', importance: 'high' },
      { id: 10, name: '静定桁架', type: 'topic', importance: 'medium' }
    ],
    edges: [
      { source: 1, target: 2, weight: 0.9 },
      { source: 2, target: 3, weight: 0.8 },
      { source: 3, target: 4, weight: 0.9 },
      { source: 4, target: 5, weight: 0.7 },
      { source: 5, target: 6, weight: 0.8 },
      { source: 6, target: 7, weight: 0.9 },
      { source: 4, target: 8, weight: 0.8 },
      { source: 4, target: 9, weight: 0.8 },
      { source: 4, target: 10, weight: 0.7 }
    ],
    clusters: [
      { id: 1, name: '基础理论', nodes: [1, 2, 3] },
      { id: 2, name: '内力分析', nodes: [4, 8, 9, 10] },
      { id: 3, name: '位移理论', nodes: [5, 6, 7] }
    ]
  };

  // 设置核心概念
  coreConceptsList.value = [
    { id: 1, name: '静力平衡', weight: 0.95, importance: 'high' },
    { id: 2, name: '几何组成', weight: 0.90, importance: 'high' },
    { id: 3, name: '内力分析', weight: 0.88, importance: 'high' },
    { id: 4, name: '虚功原理', weight: 0.75, importance: 'medium' }
  ];

  // 设置学习路径
  learningPaths.value = [
    { id: 1, name: '基础路径', steps: ['力学基础', '静力平衡', '几何组成', '内力分析'] },
    { id: 2, name: '进阶路径', steps: ['内力分析', '位移计算', '虚功原理', '单位荷载法'] },
    { id: 3, name: '应用路径', steps: ['静定梁', '静定刚架', '静定桁架'] }
  ];

  // 渲染图谱（同步调用）
  nextTick(() => {
    renderGraph();
  });
};

// 渲染知识图谱
const renderGraph = () => {
  // TODO: 使用D3.js或其他图形库渲染知识图谱
  console.log('渲染知识图谱', graphData.value);
};

// 生成知识图谱
const generateGraph = async () => {
  console.log('生成知识图谱');
  await loadGraphData();
};

// 编辑图谱
const editGraph = () => {
  console.log('编辑知识图谱');
  // TODO: 实现图谱编辑功能
};

// 导出图谱
const exportGraph = () => {
  console.log('导出知识图谱');
  // TODO: 实现图谱导出功能
};

// 更新图谱显示
const updateGraphDisplay = () => {
  console.log('更新显示模式:', displayMode.value);
  renderGraph();
};

// 过滤节点
const filterNodes = () => {
  console.log('过滤节点类型');
  renderGraph();
};

// 重置视图
const resetView = () => {
  console.log('重置图谱视图');
  renderGraph();
};

// 计算复杂度
const calculateComplexity = () => {
  if (!graphData.value) return 0;
  const nodeCount = graphData.value.nodes.length;
  const edgeCount = graphData.value.edges.length;
  return Math.round((edgeCount / nodeCount) * 100) / 100;
};

// 查看学习路径
const viewPath = (path) => {
  console.log('查看学习路径:', path);
  // TODO: 实现路径查看功能
};

// 组件挂载时加载数据
onMounted(() => {
  // 初始化时不自动加载，等待用户点击生成
  console.log('知识图谱组件已挂载，课程ID:', props.courseId);
});
</script>

<style scoped>
/* 知识图谱样式 */
.course-knowledge-graph {
  background-color: var(--background-color, #ffffff);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--background-color, #ffffff);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color, #1f2937);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 0.75rem;
}

/* 图谱控制面板 */
.graph-controls {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 1rem 1.5rem;
  background-color: var(--background-color-secondary, #f9fafb);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.control-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color, #374151);
}

.control-select {
  padding: 0.25rem 0.5rem;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.checkbox-group {
  display: flex;
  gap: 1rem;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
}

/* 图谱容器 */
.graph-container {
  display: flex;
  height: 600px;
}

.graph-canvas {
  flex: 1;
  border-right: 1px solid var(--border-color, #e5e7eb);
  position: relative;
}

.graph-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: var(--background-color-secondary, #f9fafb);
}

.placeholder-content {
  text-align: center;
  color: var(--text-color-secondary, #6b7280);
}

.graph-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.graph-stats {
  display: flex;
  gap: 2rem;
  margin-top: 1.5rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color, #6366f1);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-color-secondary, #6b7280);
}

/* 信息面板 */
.graph-info-panel {
  width: 300px;
  background-color: var(--background-color, #ffffff);
  overflow-y: auto;
}

.panel-section {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.panel-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color, #1f2937);
  margin: 0 0 1rem 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.stat-card {
  text-align: center;
  padding: 0.75rem;
  background-color: var(--background-color-secondary, #f9fafb);
  border-radius: 0.375rem;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-color, #6366f1);
}

.stat-name {
  font-size: 0.75rem;
  color: var(--text-color-secondary, #6b7280);
  margin-top: 0.25rem;
}

/* 概念列表 */
.concepts-list {
  space-y: 0.5rem;
}

.concept-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.concept-item.high {
  background-color: #fef2f2;
  color: #991b1b;
}

.concept-item.medium {
  background-color: #fffbeb;
  color: #92400e;
}

.concept-item.low {
  background-color: #f0fdf4;
  color: #166534;
}

/* 学习路径 */
.learning-paths {
  space-y: 0.75rem;
}

.path-item {
  padding: 0.75rem;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.375rem;
}

.path-name {
  font-weight: 500;
  color: var(--text-color, #1f2937);
  margin-bottom: 0.25rem;
}

.path-steps {
  font-size: 0.875rem;
  color: var(--text-color-secondary, #6b7280);
  margin-bottom: 0.5rem;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.btn-primary {
  background-color: var(--primary-color, #6366f1);
  color: white;
}

.btn-secondary {
  background-color: var(--background-color-secondary, #f3f4f6);
  color: var(--text-color, #374151);
  border-color: var(--border-color, #d1d5db);
}

.btn-outline {
  background-color: transparent;
  color: var(--text-color, #374151);
  border-color: var(--border-color, #d1d5db);
}

.btn-small {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color, #1f2937);
  margin: 0 0 0.5rem 0;
}

.empty-description {
  color: var(--text-color-secondary, #6b7280);
  margin: 0 0 1.5rem 0;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-color-secondary, #6b7280);
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--border-color, #e5e7eb);
  border-top: 2px solid var(--primary-color, #6366f1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>

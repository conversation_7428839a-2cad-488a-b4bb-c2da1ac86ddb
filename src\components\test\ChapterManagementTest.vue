<template>
  <div class="chapter-management-test">
    <h2>章节管理功能测试</h2>
    
    <div class="test-section">
      <h3>测试参数</h3>
      <div class="form-group">
        <label>课程ID:</label>
        <input v-model="testCourseId" type="number" placeholder="输入课程ID" />
      </div>
      <div class="form-group">
        <label>图谱类型:</label>
        <select v-model="testGraphType">
          <option value="0">知识图谱</option>
          <option value="1">章节</option>
        </select>
      </div>
    </div>

    <div class="test-actions">
      <button @click="testQueryExisting" :disabled="loading">查询现有数据</button>
      <button @click="testEnsureData" :disabled="loading">确保数据存在</button>
      <button @click="testCreateNew" :disabled="loading">创建新数据</button>
    </div>

    <div v-if="loading" class="loading">
      <p>正在执行测试...</p>
    </div>

    <div v-if="testResult" class="test-result">
      <h3>测试结果</h3>
      <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
    </div>

    <div v-if="errorMessage" class="error-message">
      <h3>错误信息</h3>
      <p>{{ errorMessage }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import {
  getGraphList,
  createGraph,
  ensureGraphData,
  ensureKnowledgeGraph,
  ensureChapterData
} from '@/api/graph';

// 响应式数据
const testCourseId = ref(1);
const testGraphType = ref('1');
const loading = ref(false);
const testResult = ref(null);
const errorMessage = ref('');

// 清除结果
const clearResults = () => {
  testResult.value = null;
  errorMessage.value = '';
};

// 测试查询现有数据
const testQueryExisting = async () => {
  loading.value = true;
  clearResults();
  
  try {
    console.log('测试查询现有数据...');
    const result = await getGraphList({
      zstpGraph: {
        courseId: testCourseId.value,
        graphType: testGraphType.value
      },
      pageNum: 1,
      pageSize: 10
    });
    
    testResult.value = {
      action: '查询现有数据',
      success: true,
      data: result
    };
    
    console.log('查询结果:', result);
  } catch (error) {
    console.error('查询失败:', error);
    errorMessage.value = `查询失败: ${error.message}`;
  } finally {
    loading.value = false;
  }
};

// 测试确保数据存在
const testEnsureData = async () => {
  loading.value = true;
  clearResults();
  
  try {
    console.log('测试确保数据存在...');
    let result;
    
    if (testGraphType.value === '0') {
      result = await ensureKnowledgeGraph(testCourseId.value, '测试知识图谱', '测试知识图谱内容');
    } else {
      result = await ensureChapterData(testCourseId.value, '测试章节', '测试章节内容');
    }
    
    testResult.value = {
      action: '确保数据存在',
      success: true,
      data: result
    };
    
    console.log('确保数据结果:', result);
  } catch (error) {
    console.error('确保数据失败:', error);
    errorMessage.value = `确保数据失败: ${error.message}`;
  } finally {
    loading.value = false;
  }
};

// 测试创建新数据
const testCreateNew = async () => {
  loading.value = true;
  clearResults();
  
  try {
    console.log('测试创建新数据...');
    const createData = {
      name: `测试${testGraphType.value === '0' ? '知识图谱' : '章节'}_${Date.now()}`,
      content: `测试内容_${Date.now()}`,
      courseId: testCourseId.value,
      graphType: testGraphType.value,
      createBy: 'test_user',
      createTime: new Date().toISOString(),
      updateBy: 'test_user',
      updateTime: new Date().toISOString(),
      remark: '测试创建的数据',
      params: {}
    };
    
    const result = await createGraph(createData);
    
    testResult.value = {
      action: '创建新数据',
      success: true,
      data: result,
      createData: createData
    };
    
    console.log('创建结果:', result);
  } catch (error) {
    console.error('创建失败:', error);
    errorMessage.value = `创建失败: ${error.message}`;
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.chapter-management-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.form-group {
  margin-bottom: 10px;
}

.form-group label {
  display: inline-block;
  width: 100px;
  font-weight: bold;
}

.form-group input,
.form-group select {
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 3px;
  width: 200px;
}

.test-actions {
  margin-bottom: 20px;
}

.test-actions button {
  margin-right: 10px;
  padding: 10px 15px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.test-actions button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.test-actions button:hover:not(:disabled) {
  background-color: #0056b3;
}

.loading {
  padding: 20px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.test-result {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 5px;
}

.test-result pre {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 3px;
  overflow-x: auto;
  white-space: pre-wrap;
}

.error-message {
  padding: 15px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 5px;
  color: #721c24;
}
</style>
